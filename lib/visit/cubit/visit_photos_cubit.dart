import 'dart:convert';
import 'dart:io';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:path_provider/path_provider.dart';

import 'package:serwis_app/core/models/visit/visit.dart';
import 'package:serwis_app/core/services/photo_service.dart';
import 'package:serwis_app/visit/repository/visit_repository.dart';

enum VisitPhotosStatus {
  loading,
  takingPhoto,
  uploadingPhoto,
  ready,
}

class VisitPhotosState {
  final VisitPhotosStatus status;
  final dynamic error;
  final List<File> photos;
  final List<String> failedPhotos;

  const VisitPhotosState({
    required this.status,
    this.error,
    this.photos = const [],
    this.failedPhotos = const [],
  });

  VisitPhotosState copyWith({
    VisitPhotosStatus? status,
    dynamic error,
    List<File>? photos,
    List<String>? failedPhotos,
  }) {
    return VisitPhotosState(
      status: status ?? this.status,
      error: error ?? this.error,
      photos: photos ?? this.photos,
      failedPhotos: failedPhotos ?? this.failedPhotos,
    );
  }
}

class VisitPhotosCubit extends Cubit<VisitPhotosState> {
  final PhotoService photoService;
  final VisitRepository visitRepository;
  final Visit visit;

  VisitPhotosCubit({
    required this.photoService,
    required this.visitRepository,
    required this.visit,
  }) : super(
          VisitPhotosState(
            status: VisitPhotosStatus.loading,
          ),
        );

  Future<void> loadPhotos() async {
    try {
      if (state.status != VisitPhotosStatus.uploadingPhoto) {
        emit(state.copyWith(status: VisitPhotosStatus.loading));
      }

      final list = await visitRepository.getVisitPhotos(visit.id);
      final photos = <File>[];
      final failedPhotos = <String>[];
      final tempDir = await getTemporaryDirectory();

      for (final photoItem in list.data) {
        try {
          final photoContent = photoItem.thumbnailContent;
          if (photoContent != null) {
            final fileName = photoItem.filename?.split('/').last ?? 'photo_${photoItem.id}.jpg';
            final photoData = base64Decode(photoContent);

            final photoPath = '${tempDir.path}/$fileName';
            final photoFile = await File(photoPath).create();
            await photoFile.writeAsBytes(photoData);

            photos.add(photoFile);
          } else {
            failedPhotos.add(photoItem.filename ?? 'Uszkodzone zdjęcie ${photoItem.id}');
          }
        } catch (e) {
          failedPhotos.add(photoItem.filename ?? 'Uszkodzone zdjęcie ${photoItem.id}');
        }
      }

      emit(
        state.copyWith(
          status: VisitPhotosStatus.ready,
          photos: photos,
          failedPhotos: failedPhotos,
        ),
      );
    } catch (e) {
      emit(state.copyWith(status: VisitPhotosStatus.ready, error: e));
    }
  }

  Future<void> takePhoto() async {
    try {
      emit(state.copyWith(status: VisitPhotosStatus.takingPhoto));

      final photo = await photoService.takePhoto();

      emit(state.copyWith(status: VisitPhotosStatus.uploadingPhoto));

      if (photo != null) {
        await visitRepository.addVisitPhoto(visit.id, photo);
        // Przeładuj zdjęcia po dodaniu nowego
        await loadPhotos();
      } else {
        emit(state.copyWith(status: VisitPhotosStatus.ready));
      }
    } catch (e) {
      emit(state.copyWith(status: VisitPhotosStatus.ready, error: e));
    }
  }
}
