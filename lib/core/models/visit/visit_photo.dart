import 'dart:convert';

class VisitPhoto {
  final int id;
  final String? filename;
  final String? category;
  final String? subCategory;
  final String? thumbnailContent; // base64 encoded thumbnail
  VisitPhoto({
    required this.id,
    this.filename,
    this.category,
    this.subCategory,
    this.thumbnailContent,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'filename': filename,
      'category': category,
      'subCategory': subCategory,
      'thumbnailContent': thumbnailContent,
    };
  }

  factory VisitPhoto.fromMap(Map<String, dynamic> map) {
    return VisitPhoto(
      id: map['id']?.toInt() ?? 0,
      filename: map['filename'],
      category: map['category'],
      subCategory: map['subCategory'],
      thumbnailContent: map['thumbnailContent'],
    );
  }

  String toJson() => json.encode(toMap());

  factory VisitPhoto.fromJson(String source) => VisitPhoto.fromMap(json.decode(source));
}

/// Wrapper dla cache'owanych zdj<PERSON>ć wizyty z metadata
class VisitPhotosCache {
  final List<VisitPhoto> photos;
  final DateTime cachedAt;
  final int visitId;

  const VisitPhotosCache({
    required this.photos,
    required this.cachedAt,
    required this.visitId,
  });

  /// Sprawdza czy cache jest jeszcze ważny (domyślnie 7 dni)
  bool isValid({Duration ttl = const Duration(days: 7)}) {
    return DateTime.now().difference(cachedAt) < ttl;
  }

  Map<String, dynamic> toMap() {
    return {
      'photos': photos.map((photo) => photo.toMap()).toList(),
      'cachedAt': cachedAt.millisecondsSinceEpoch,
      'visitId': visitId,
    };
  }

  factory VisitPhotosCache.fromMap(Map<String, dynamic> map) {
    return VisitPhotosCache(
      photos: List<VisitPhoto>.from(
        map['photos']?.map((x) => VisitPhoto.fromMap(x)) ?? [],
      ),
      cachedAt: DateTime.fromMillisecondsSinceEpoch(map['cachedAt'] ?? 0),
      visitId: map['visitId']?.toInt() ?? 0,
    );
  }

  String toJson() => json.encode(toMap());

  factory VisitPhotosCache.fromJson(String source) => VisitPhotosCache.fromMap(json.decode(source));
}
